# -*- coding: utf-8 -*-
"""
主窗口
Main Window

完全按照HTML设计实现的主窗口布局
包含侧边栏、顶部栏和内容区域
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
    QStackedWidget, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon

from ui.components.sidebar import Sidebar
from ui.components.topbar import TopBar
from ui.pages.patients_page import PatientsPage
from ui.pages.treatment_page import TreatmentPage
from ui.pages.reports_page import ReportsPage
from ui.pages.users_page import UsersPage
from ui.pages.settings_page import SettingsPage


class MainWindow(QMainWindow):
    """主窗口类 - 完全按照HTML设计实现"""
    
    # 窗口信号
    window_closed = Signal()
    page_changed = Signal(str)
    theme_changed = Signal(str)
    
    def __init__(self, config=None):
        super().__init__()

        # 保存配置实例
        self.config = config

        # 当前页面
        self.current_page = "patients"

        # 从配置文件加载主题设置
        self.current_theme = self._load_theme_from_config()

        # UI组件
        self.sidebar = None
        self.topbar = None
        self.content_area = None
        self.page_stack = None

        # 页面映射
        self.pages = {}

        # 强制关闭标志，用于避免重复确认
        self._force_close = False

        # 初始化UI
        self._init_ui()
        self._setup_window()
        self._connect_signals()
        self._create_pages()

        # 设置主题切换器的初始状态
        self._init_theme_switch()

        # 应用初始主题到所有页面组件（确保实时曲线等组件正确应用config.json中的主题设置）
        self.update_theme()

        # 设置默认页面
        self.switch_to_page("patients")

        # 初始化患者数量
        self.update_patients_count()

        # 延迟启动数据同步服务（5分钟后）
        self._setup_delayed_sync()
    
    def _init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局 - 水平布局（侧边栏 + 主内容区）
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = Sidebar()
        main_layout.addWidget(self.sidebar)
        
        # 创建主内容区域
        self.main_content = self._create_main_content()
        main_layout.addWidget(self.main_content, 1)
    
    def _create_main_content(self) -> QWidget:
        """创建主内容区域"""
        main_content = QWidget()
        main_content.setObjectName("main_content")
        
        # 垂直布局（顶部栏 + 内容区域）
        layout = QVBoxLayout(main_content)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 顶部栏
        self.topbar = TopBar()
        layout.addWidget(self.topbar)
        
        # 内容区域
        self.content_area = QWidget()
        self.content_area.setObjectName("content_area")
        
        content_layout = QVBoxLayout(self.content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 页面堆栈
        self.page_stack = QStackedWidget()
        self.page_stack.setObjectName("page_stack")
        content_layout.addWidget(self.page_stack)
        
        layout.addWidget(self.content_area, 1)
        
        return main_content
    
    def _setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("脑机接口康复训练系统 V2.0.0")
        self.setMinimumSize(1200, 680)
        
        # 设置窗口图标
        from utils.path_manager import get_icon_path
        icon_path = get_icon_path("ht.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        else:
            print(f"⚠️ 窗口图标文件不存在：{icon_path}")
        
        # 设置初始大小和位置
        self.resize(1300, 600)
        self._center_window()
    
    def _center_window(self):
        """窗口居中显示"""
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        
        self.move(x, y)
    
    def _connect_signals(self):
        """连接信号槽"""
        # 侧边栏信号
        self.sidebar.navigation_changed.connect(self._on_navigation_changed)
        self.sidebar.sidebar_toggled.connect(self._on_sidebar_toggled)
        
        # 顶部栏信号
        self.topbar.menu_toggle_clicked.connect(self._on_menu_toggle)
        self.topbar.theme_changed.connect(self._on_theme_changed)
    
    def _create_pages(self):
        """创建所有页面"""
        # 患者管理页面
        patients_page = PatientsPage()
        self.pages["patients"] = patients_page
        self.page_stack.addWidget(patients_page)
        
        # 治疗系统页面
        treatment_page = TreatmentPage()
        self.pages["treatment"] = treatment_page
        self.page_stack.addWidget(treatment_page)
        
        # 报告分析页面
        reports_page = ReportsPage()
        self.pages["reports"] = reports_page
        self.page_stack.addWidget(reports_page)
        
        # 用户管理页面
        users_page = UsersPage()
        self.pages["users"] = users_page
        self.page_stack.addWidget(users_page)
        
        # 系统设置页面
        settings_page = SettingsPage()
        self.pages["settings"] = settings_page
        self.page_stack.addWidget(settings_page)

        # 连接设置页面信号
        settings_page.settings_saved.connect(self._on_settings_saved)

        # 连接治疗页面信号
        treatment_page.treatment_stopped.connect(self._on_treatment_completed)
    
    def _on_navigation_changed(self, page_id: str):
        """导航切换处理"""
        if page_id == "exit":
            # 处理退出系统
            self._show_exit_confirmation()
        else:
            # 检查页面访问权限
            if self._check_page_access_permission(page_id):
                self.switch_to_page(page_id)
            else:
                self._show_access_denied_message(page_id)
    
    def _check_page_access_permission(self, page_id: str) -> bool:
        """检查页面访问权限"""
        try:
            from utils.simple_permission_manager import permission_manager
            return permission_manager.can_access_page(page_id)
        except Exception as e:
            print(f"权限检查失败: {e}")
            return True  # 默认允许访问，避免系统崩溃
    
    def _show_access_denied_message(self, page_id: str):
        """显示访问被拒绝消息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(
            self,
            "访问被拒绝",
            f"您没有权限访问 {page_id} 页面。\n\n请联系管理员获取相应权限。"
        )
    
    def _on_sidebar_toggled(self, collapsed: bool):
        """侧边栏切换处理"""
        # 可以在这里添加额外的处理逻辑
        pass
    
    def _on_menu_toggle(self):
        """菜单切换按钮点击"""
        self.sidebar.toggle_collapse()
    
    def _on_theme_changed(self, theme: str):
        """主题切换处理"""
        self.current_theme = theme
        self.theme_changed.emit(theme)

        # 保存主题设置到配置文件
        self._save_theme_to_config(theme)

        # 应用新主题
        self._apply_theme(theme)

        # 更新所有页面的主题
        self.update_theme()
    
    def _apply_theme(self, theme: str):
        """应用主题"""
        try:
            from ui.themes.theme_manager import ThemeManager
            theme_manager = ThemeManager()
            stylesheet = theme_manager.get_stylesheet(theme)
            
            # 应用到整个应用
            from PySide6.QtWidgets import QApplication
            QApplication.instance().setStyleSheet(stylesheet)
            
        except Exception as e:
            print(f"主题应用失败: {e}")

    def _save_theme_to_config(self, theme: str):
        """保存主题设置到配置文件"""
        try:
            if self.config:
                self.config.set('theme', theme)
                self.config.save()
                # print(f"主题设置已保存: {theme}")
            else:
                # 如果没有配置实例，创建新的
                from app.config import AppConfig
                config = AppConfig()
                config.set('theme', theme)
                config.save()
                # print(f"主题设置已保存（新配置实例）: {theme}")
        except Exception as e:
            print(f"保存主题设置失败: {e}")

    def _load_theme_from_config(self) -> str:
        """从配置文件加载主题设置"""
        try:
            if self.config:
                theme = self.config.get('theme', 'tech')  # 默认科技主题
                return theme
            else:
                # 如果没有配置实例，创建新的
                from app.config import AppConfig
                config = AppConfig()
                theme = config.get('theme', 'tech')  # 默认科技主题
                return theme
        except Exception as e:
            print(f"加载主题设置失败，使用默认主题: {e}")
            return 'tech'

    def _init_theme_switch(self):
        """初始化主题切换器状态"""
        if self.topbar and hasattr(self.topbar, 'theme_switch'):
            self.topbar.set_theme(self.current_theme)

    def update_theme(self):
        """更新主题 - 通知所有页面更新主题"""
        # 更新所有页面的主题
        for page_id, page in self.pages.items():
            if hasattr(page, 'update_theme'):
                page.update_theme()

    def switch_to_page(self, page_id: str):
        """切换到指定页面"""
        if page_id in self.pages:
            # 切换页面
            page_widget = self.pages[page_id]
            self.page_stack.setCurrentWidget(page_widget)

            # 更新当前页面
            self.current_page = page_id

            # 更新侧边栏激活状态
            self.sidebar.set_active_item(page_id)

            # 更新顶部栏标题
            self.topbar.update_page_info(page_id)

            # 发送信号
            self.page_changed.emit(page_id)

    def switch_to_treatment_with_patient(self, patient_data: dict):
        """切换到治疗系统页面并设置患者信息"""
        # 先切换到治疗系统页面
        self.switch_to_page("treatment")

        # 获取治疗系统页面并设置患者信息
        treatment_page = self.pages.get("treatment")
        if treatment_page and hasattr(treatment_page, 'set_patient_info'):
            treatment_page.set_patient_info(patient_data)

    def update_patients_count(self):
        """更新侧边栏患者管理徽章的数量"""
        try:
            from services.patient_service import patient_service
            count = patient_service.get_current_patients_count()
            self.sidebar.update_patients_badge(count)
        except Exception as e:
            print(f"更新患者数量失败: {e}")
    
    def get_current_page(self) -> str:
        """获取当前页面ID"""
        return self.current_page

    def update_user_info(self, user_info: dict):
        """更新用户信息显示"""
        if hasattr(self, 'sidebar'):
            self.sidebar.update_user_info(user_info)

    def cleanup(self):
        """清理资源"""
        # 清理各个页面
        for page in self.pages.values():
            if hasattr(page, 'cleanup'):
                page.cleanup()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果是强制关闭（从菜单退出），跳过确认但仍需发送UDP退出指令
        if hasattr(self, '_force_close') and self._force_close:
            # 重置标志
            self._force_close = False
            # 发送关闭信号
            self.window_closed.emit()
            # 清理资源
            self.cleanup()
            # 接受关闭事件
            event.accept()
            return
        
        # 显示退出确认对话框（内部会发送UDP退出指令）
        if self._confirm_exit():
            # 用户确认退出
            # 发送关闭信号
            self.window_closed.emit()
            
            # 清理资源
            self.cleanup()

            # 接受关闭事件
            event.accept()
        else:
            # 用户取消退出，忽略关闭事件
            event.ignore()

    def _on_settings_saved(self, settings_data: dict):
        """设置保存处理"""
        try:
            # 更新治疗页面的参数
            treatment_page = self.pages.get("treatment")
            if treatment_page:
                # 刷新治疗参数
                if hasattr(treatment_page, 'refresh_settings_parameters'):
                    treatment_page.refresh_settings_parameters()
                    print("治疗页面参数已刷新")

                # 更新蓝牙配置（如果方法存在）
                if hasattr(treatment_page, 'load_bluetooth_settings'):
                    treatment_page.load_bluetooth_settings(settings_data)
                    print("治疗页面蓝牙配置已更新")

        except Exception as e:
            print(f"更新治疗页面配置失败: {e}")

    def _on_treatment_completed(self):
        """治疗完成处理"""
        try:
            # 刷新患者页面的治疗记录
            patients_page = self.pages.get("patients")
            if patients_page and hasattr(patients_page, 'refresh_current_patient_details'):
                patients_page.refresh_current_patient_details()
                print("患者治疗记录已刷新")

        except Exception as e:
            print(f"刷新患者治疗记录失败: {e}")

    def _show_exit_confirmation(self):
        """显示退出系统确认对话框"""
        from ui.components.themed_message_box import show_question
        from PySide6.QtWidgets import QMessageBox
        
        result = show_question(
            self,
            "退出系统",
            "确定要退出脑机接口康复训练系统吗？\n\n退出后所有未保存的数据将丢失。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if result == QMessageBox.StandardButton.Yes:
            # 用户确认退出，发送UDP退出指令
            self._send_exit_command()
            # 强制关闭应用程序（跳过closeEvent中的再次确认）
            self._force_close = True
            self.close()

    def _send_exit_command(self):
        """发送UDP退出指令到VR系统"""
        try:
            from core.udp_communicator import get_udp_communicator
            udp_comm = get_udp_communicator()
            
            # 确保UDP通信器已启动
            if not udp_comm.is_connected:
                udp_comm.start_listening()
            
            # 发送退出指令
            success = udp_comm.send_exit_command()
            if success:
                print("UDP退出指令发送成功")
            else:
                print("UDP退出指令发送失败")
            
            # 停止UDP监听
            udp_comm.stop_listening()
                
        except Exception as e:
            print(f"发送UDP退出指令时发生错误: {e}")

    def _confirm_exit(self):
        """确认退出对话框，返回用户选择"""
        from ui.components.themed_message_box import show_question
        from PySide6.QtWidgets import QMessageBox
        
        result = show_question(
            self,
            "退出系统",
            "确定要退出脑机接口康复训练系统吗？\n\n退出后所有未保存的数据将丢失。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if result == QMessageBox.StandardButton.Yes:
            # 用户确认退出，发送UDP退出指令
            self._send_exit_command()
            return True

    def _setup_delayed_sync(self):
        """设置延迟数据同步"""
        try:
            from PySide6.QtCore import QTimer

            # 创建延迟同步定时器（5分钟 = 300000毫秒）
            self.sync_timer = QTimer()
            self.sync_timer.setSingleShot(True)  # 只触发一次
            self.sync_timer.timeout.connect(self._start_data_sync)

            # 启动定时器
            self.sync_timer.start(300000)  # 5分钟延迟
            # print("⏰ 数据同步服务将在5分钟后启动")

        except Exception as e:
            print(f"设置延迟同步失败: {e}")

    def _start_data_sync(self):
        """启动数据同步"""
        try:
            from services.data_sync_service import data_sync_manager

            # 连接同步完成信号
            data_sync_manager.sync_finished.connect(self._on_sync_finished)

            # 启动同步
            data_sync_manager.start_sync()

        except Exception as e:
            print(f"启动数据同步失败: {e}")

    def _on_sync_finished(self, patient_success: int, patient_total: int,
                         treatment_success: int, treatment_total: int):
        """数据同步完成处理"""
        try:
            if patient_total > 0 or treatment_total > 0:
                print(f"📊 后台数据同步完成：患者 {patient_success}/{patient_total}，治疗记录 {treatment_success}/{treatment_total}")

            # 断开信号连接，避免重复处理
            from services.data_sync_service import data_sync_manager
            data_sync_manager.sync_finished.disconnect(self._on_sync_finished)

        except Exception as e:
            print(f"处理同步结果失败: {e}")
        
        return False
