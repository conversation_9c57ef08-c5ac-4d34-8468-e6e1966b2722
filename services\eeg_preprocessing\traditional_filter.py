"""
传统滤波处理器

实现三步传统滤波预处理：
1. 陷波滤波器（去除50Hz工频干扰）
2. 带通滤波器（保留4-40Hz有效频段）
3. 通道标准化（Z-score标准化）
"""

import numpy as np
from scipy import signal
import time
import logging
from typing import Optional, Tuple

from .preprocessing_config import PreprocessingConfig


class TraditionalFilterProcessor:
    """传统滤波处理器"""
    
    def __init__(self, config: PreprocessingConfig):
        """
        初始化传统滤波处理器
        
        Args:
            config: 预处理配置对象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 性能统计
        self.processing_times = []
        self.processed_count = 0

        # 实时滤波缓冲区（用于短数据的连续滤波）
        self.buffer_size = 250  # 2秒的数据缓冲（125Hz * 2s）
        self.data_buffer = np.zeros((config.n_channels, self.buffer_size))
        self.buffer_index = 0
        self.buffer_filled = False

        # 滤波器状态（用于连续滤波）
        self.notch_zi = None
        self.bandpass_zi = None

        # 初始化滤波器
        self._init_filters()
        
        self.logger.info("传统滤波处理器初始化完成")
    
    def _init_filters(self):
        """初始化滤波器系数和状态"""
        try:
            # 陷波滤波器（去除50Hz工频干扰）
            self.notch_b, self.notch_a = signal.iirnotch(
                self.config.notch_freq,
                Q=self.config.notch_q,
                fs=self.config.sampling_rate
            )

            # 带通滤波器（4-40Hz）
            self.bandpass_b, self.bandpass_a = signal.butter(
                self.config.bandpass_order,
                [self.config.bandpass_low, self.config.bandpass_high],
                btype='band',
                fs=self.config.sampling_rate
            )

            # 初始化滤波器状态（用于实时滤波）
            self.notch_zi = np.zeros((self.config.n_channels, len(self.notch_a) - 1))
            self.bandpass_zi = np.zeros((self.config.n_channels, len(self.bandpass_a) - 1))

            self.logger.info(f"滤波器初始化成功 - 陷波:{self.config.notch_freq}Hz(Q={self.config.notch_q}), "
                           f"带通:{self.config.bandpass_low}-{self.config.bandpass_high}Hz")

        except Exception as e:
            self.logger.error(f"滤波器初始化失败: {e}")
            raise
    
    def process(self, eeg_data: np.ndarray) -> dict:
        """
        执行传统滤波处理
        
        Args:
            eeg_data: 输入EEG数据 [8, 4] - 8通道，4个样本点
            
        Returns:
            dict: 包含处理结果和统计信息
        """
        start_time = time.perf_counter()
        
        try:
            # 检查输入数据格式
            if eeg_data.shape != (self.config.n_channels, 4):
                raise ValueError(f"输入数据形状错误: {eeg_data.shape}, 期望: ({self.config.n_channels}, 4)")
            
            result = {
                'input_data': eeg_data.copy(),
                'filtered_data': None,
                'processing_time': 0,
                'steps_applied': []
            }
            
            # 实时滤波处理：将新数据添加到缓冲区并进行滤波
            processed_data = self._realtime_filtering(eeg_data)

            # 步骤3：通道标准化（逐包标准化默认关闭，防止窗口内统计不一致）
            if getattr(self.config, 'enable_packet_standardization', False):
                standardized_data = self._channel_standardization(processed_data)
                result['steps_applied'].extend(['realtime_notch_filter', 'realtime_bandpass_filter', 'packet_channel_standardization'])
                result['filtered_data'] = standardized_data
                result['filtered_data_before_standardization'] = processed_data  # 保存归一化前的数据
            else:
                result['steps_applied'].extend(['realtime_notch_filter', 'realtime_bandpass_filter'])
                result['filtered_data'] = processed_data
            
            # 记录处理时间
            processing_time = (time.perf_counter() - start_time) * 1000  # 转换为毫秒
            result['processing_time'] = processing_time
            
            # 性能统计
            self._update_performance_stats(processing_time)
            
            if self.config.enable_debug_output:
                self.logger.debug(f"传统滤波处理完成，耗时: {processing_time:.3f}ms")
            
            return result
            
        except Exception as e:
            self.logger.error(f"传统滤波处理失败: {e}")
            return {
                'input_data': eeg_data.copy(),
                'filtered_data': None,
                'processing_time': (time.perf_counter() - start_time) * 1000,
                'error': str(e),
                'steps_applied': []
            }
    
    def process_long_data(self, eeg_data: np.ndarray) -> dict:
        """
        处理长数据（用于离线分析或缓存足够数据时）
        
        Args:
            eeg_data: 输入EEG数据 [8, n_samples] - 8通道，n个样本点
            
        Returns:
            dict: 包含处理结果和统计信息
        """
        start_time = time.perf_counter()
        
        try:
            if eeg_data.shape[0] != self.config.n_channels:
                raise ValueError(f"通道数错误: {eeg_data.shape[0]}, 期望: {self.config.n_channels}")
            
            if eeg_data.shape[1] < 10:  # 至少需要10个样本点进行有效滤波
                self.logger.warning(f"数据点太少({eeg_data.shape[1]})，滤波效果可能不佳")
            
            result = {
                'input_data': eeg_data.copy(),
                'filtered_data': None,
                'processing_time': 0,
                'steps_applied': []
            }
            
            processed_data = eeg_data.copy()
            
            # 步骤1：陷波滤波
            for ch in range(self.config.n_channels):
                processed_data[ch] = signal.filtfilt(
                    self.notch_b, self.notch_a, processed_data[ch]
                )
            result['steps_applied'].append('notch_filter')
            
            # 步骤2：带通滤波
            for ch in range(self.config.n_channels):
                processed_data[ch] = signal.filtfilt(
                    self.bandpass_b, self.bandpass_a, processed_data[ch]
                )
            result['steps_applied'].append('bandpass_filter')
            
            # 步骤3：通道标准化（离线长数据路径默认关闭，避免与窗口标准化冲突）
            if getattr(self.config, 'enable_offline_standardization', False):
                # 保存归一化前的数据
                result['filtered_data_before_standardization'] = processed_data.copy()
                processed_data = self._channel_standardization(processed_data)
                result['steps_applied'].append('offline_channel_standardization')
            
            result['filtered_data'] = processed_data
            
            # 记录处理时间
            processing_time = (time.perf_counter() - start_time) * 1000
            result['processing_time'] = processing_time
            
            self._update_performance_stats(processing_time)
            
            if self.config.enable_debug_output:
                self.logger.debug(f"长数据传统滤波处理完成，耗时: {processing_time:.3f}ms")
            
            return result
            
        except Exception as e:
            self.logger.error(f"长数据传统滤波处理失败: {e}")
            return {
                'input_data': eeg_data.copy(),
                'filtered_data': None,
                'processing_time': (time.perf_counter() - start_time) * 1000,
                'error': str(e),
                'steps_applied': []
            }
    
    def _channel_standardization(self, eeg_data: np.ndarray) -> np.ndarray:
        """
        通道标准化（Z-score标准化）
        
        Args:
            eeg_data: 输入数据 [n_channels, n_samples]
            
        Returns:
            标准化后的数据
        """
        try:
            # 计算每个通道的均值和标准差
            channel_means = np.mean(eeg_data, axis=1, keepdims=True)
            channel_stds = np.std(eeg_data, axis=1, keepdims=True)
            
            # 防止除零
            channel_stds = np.where(
                channel_stds < self.config.standardization_epsilon,
                self.config.standardization_epsilon,
                channel_stds
            )
            
            # Z-score标准化
            standardized_data = (eeg_data - channel_means) / channel_stds
            
            return standardized_data
            
        except Exception as e:
            self.logger.error(f"通道标准化失败: {e}")
            return eeg_data  # 返回原始数据

    def _realtime_filtering(self, eeg_data: np.ndarray) -> np.ndarray:
        """
        实时滤波处理（使用缓冲区和滤波器状态）

        Args:
            eeg_data: 新的EEG数据 [n_channels, 4]

        Returns:
            滤波后的数据 [n_channels, 4]
        """
        try:
            n_channels, n_samples = eeg_data.shape
            filtered_data = np.zeros_like(eeg_data)

            # 逐样本处理以维护滤波器状态
            for sample_idx in range(n_samples):
                current_sample = eeg_data[:, sample_idx]

                # 步骤1：陷波滤波（使用lfilter维护状态）
                notch_output = np.zeros(n_channels)
                for ch in range(n_channels):
                    notch_output[ch], self.notch_zi[ch] = signal.lfilter(
                        self.notch_b, self.notch_a,
                        [current_sample[ch]],
                        zi=self.notch_zi[ch]
                    )

                # 步骤2：带通滤波
                bandpass_output = np.zeros(n_channels)
                for ch in range(n_channels):
                    bandpass_output[ch], self.bandpass_zi[ch] = signal.lfilter(
                        self.bandpass_b, self.bandpass_a,
                        [notch_output[ch]],
                        zi=self.bandpass_zi[ch]
                    )

                filtered_data[:, sample_idx] = bandpass_output.flatten()

                # 更新数据缓冲区
                self.data_buffer[:, self.buffer_index] = bandpass_output.flatten()
                self.buffer_index = (self.buffer_index + 1) % self.buffer_size
                if self.buffer_index == 0:
                    self.buffer_filled = True

            return filtered_data

        except Exception as e:
            self.logger.error(f"实时滤波处理失败: {e}")
            return eeg_data

    def get_buffered_data(self, n_samples: int = None) -> np.ndarray:
        """
        获取缓冲区中的历史数据

        Args:
            n_samples: 需要的样本数，None表示获取所有可用数据

        Returns:
            历史数据 [n_channels, n_samples]
        """
        if not self.buffer_filled and self.buffer_index == 0:
            return np.array([]).reshape(self.config.n_channels, 0)

        if n_samples is None:
            if self.buffer_filled:
                # 重新排列缓冲区数据以获得正确的时间顺序
                ordered_data = np.concatenate([
                    self.data_buffer[:, self.buffer_index:],
                    self.data_buffer[:, :self.buffer_index]
                ], axis=1)
                return ordered_data
            else:
                return self.data_buffer[:, :self.buffer_index]
        else:
            # 获取最近的n_samples个样本
            if self.buffer_filled:
                total_samples = self.buffer_size
            else:
                total_samples = self.buffer_index

            if n_samples > total_samples:
                n_samples = total_samples

            if self.buffer_filled:
                start_idx = (self.buffer_index - n_samples) % self.buffer_size
                if start_idx + n_samples <= self.buffer_size:
                    return self.data_buffer[:, start_idx:start_idx + n_samples]
                else:
                    return np.concatenate([
                        self.data_buffer[:, start_idx:],
                        self.data_buffer[:, :n_samples - (self.buffer_size - start_idx)]
                    ], axis=1)
            else:
                start_idx = max(0, self.buffer_index - n_samples)
                return self.data_buffer[:, start_idx:self.buffer_index]

    def reset_filter_states(self):
        """重置滤波器状态和缓冲区"""
        if hasattr(self, 'notch_zi'):
            self.notch_zi.fill(0)
        if hasattr(self, 'bandpass_zi'):
            self.bandpass_zi.fill(0)

        self.data_buffer.fill(0)
        self.buffer_index = 0
        self.buffer_filled = False

        self.logger.info("滤波器状态和缓冲区已重置")

    def _update_performance_stats(self, processing_time: float):
        """更新性能统计"""
        self.processing_times.append(processing_time)
        self.processed_count += 1
        
        # 保持最近1000次的记录
        if len(self.processing_times) > 1000:
            self.processing_times = self.processing_times[-1000:]
        
        # 定期输出性能统计
        if (self.config.enable_performance_monitoring and 
            self.processed_count % self.config.performance_log_interval == 0):
            self._log_performance_stats()
    
    def _log_performance_stats(self):
        """输出性能统计信息"""
        if not self.processing_times:
            return
        
        avg_time = np.mean(self.processing_times)
        max_time = np.max(self.processing_times)
        min_time = np.min(self.processing_times)
        
        self.logger.info(f"传统滤波性能统计 - 平均: {avg_time:.3f}ms, "
                        f"最大: {max_time:.3f}ms, 最小: {min_time:.3f}ms, "
                        f"处理次数: {self.processed_count}")
    
    def get_performance_stats(self) -> dict:
        """获取性能统计信息"""
        if not self.processing_times:
            return {"processed_count": 0}
        
        return {
            "processed_count": self.processed_count,
            "avg_processing_time": np.mean(self.processing_times),
            "max_processing_time": np.max(self.processing_times),
            "min_processing_time": np.min(self.processing_times),
            "recent_times": self.processing_times[-10:] if len(self.processing_times) >= 10 else self.processing_times
        }
    
    def reset_stats(self):
        """重置性能统计"""
        self.processing_times = []
        self.processed_count = 0
        self.logger.info("传统滤波性能统计已重置")
